import GSAPTextReveal from '@/components/GSAPTextReveal'
import { getPreset } from '@/components/GSAPTextReveal/presets'
import styles from './style.module.scss'
import { useTranslation } from '@/hooks/useTranslation'

export default function Hero({
  title,
  subtitle,
  locale = 'fr',
  containerClass = 'container'
}) {
  const { t } = useTranslation('pages');

  const defaultTitle = t('home.hero_title');
  const defaultSubtitle = t('home.hero_subtitle');

  return (
    <main className={`${containerClass} default-hero`}>
      <GSAPTextReveal
        as="h1"
        className={styles.heading}
        {...getPreset('hero')}
      >
        {title || defaultTitle}
      </GSAPTextReveal>
      {(subtitle || defaultSubtitle) && (
        <GSAPTextReveal
          as="p"
          className="text-big"
          {...getPreset('subtitle', { delay: 0.5 })}
        >
          {subtitle || defaultSubtitle}
        </GSAPTextReveal>
      )}
    </main>
  )
}


