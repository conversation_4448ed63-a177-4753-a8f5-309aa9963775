'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { useTranslation } from '@/hooks/useTranslation';
import GSAPTextReveal from '@/components/GSAPTextReveal';
import { getPreset } from '@/components/GSAPTextReveal/presets';
import styles from './style.module.scss';
import Hero from '@/components/Heros/Hero';


export default function Hero4({
  imgSrc = '/images/lucas-joliveau-siege-ordinateur-portable.png',
  locale = 'fr'
}) {
  const { t } = useTranslation('pages');

  return (
    <section className={styles.main}>

      <Hero
        title="Une fusée créative pour les entreprises"
        subtitle="Spécialistes du design et du développement web"
        locale={locale}
      />
      {/* Contenu principal avec texte et image */}
      <div className={`${styles.content} container`}>
        {/* Bloc de texte à gauche */}
        <GSAPTextReveal
          className={`${styles.textBlock} text-big`}
          {...getPreset('lines', { delay: 1.5, stagger: 0.15 })}
        >
          {t('agency.hero4.description')}
        </GSAPTextReveal>

        <div className={styles.imageContainer}>
            <motion.div
              className={styles.imageWrapper}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.2 }}
            >
            <Image
              src={imgSrc}
              alt={t('agency.hero4.image_alt')}
              fill
              sizes="(max-width: 768px) 100vw, 50vw"
              className={styles.image}
              priority
            />
            </motion.div>
        </div>
      </div>
    </section>
  );
}
