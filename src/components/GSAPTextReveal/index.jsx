'use client';

import { useRef, useEffect, forwardRef, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import styles from './style.module.scss';

// Enregistrer ScrollTrigger
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

/**
 * Composant d'animation de texte utilisant GSAP SplitText
 * 
 * @param {Object} props - Les propriétés du composant
 * @param {string} props.as - Le tag HTML à utiliser (par défaut 'div')
 * @param {React.ReactNode} props.children - Le contenu texte à animer
 * @param {string} props.className - Classes CSS additionnelles
 * @param {'chars'|'words'|'lines'} props.splitBy - Type de division du texte (par défaut 'chars')
 * @param {number} props.stagger - <PERSON><PERSON>lai entre chaque élément (par défaut 0.02)
 * @param {number} props.duration - Durée de l'animation (par défaut 0.6)
 * @param {number} props.delay - <PERSON><PERSON><PERSON> avant le début de l'animation (par défaut 0)
 * @param {string} props.ease - Type d'easing (par défaut 'power2.out')
 * @param {Object} props.from - État initial de l'animation
 * @param {Object} props.to - État final de l'animation
 * @param {boolean} props.triggerOnScroll - Déclencher l'animation au scroll (par défaut true)
 * @param {string} props.triggerStart - Position de déclenchement ScrollTrigger (par défaut 'top 80%')
 * @param {boolean} props.triggerOnce - Animation une seule fois (par défaut true)
 * @param {Function} props.onComplete - Callback à la fin de l'animation
 * @param {Function} props.onStart - Callback au début de l'animation
 */
const GSAPTextReveal = forwardRef(({
  as: Tag = 'div',
  children,
  className = '',
  splitBy = 'chars',
  stagger = 0.02,
  duration = 0.6,
  delay = 0,
  ease = 'power2.out',
  from = {
    clipPath: 'inset(0 0 100% 0)',
    y: 30
  },
  to = {
    clipPath: 'inset(0 0 0% 0)',
    y: 0
  },
  triggerOnScroll = true,
  triggerStart = 'top 80%',
  triggerOnce = true,
  onComplete,
  onStart,
  ...props
}, ref) => {
  const elementRef = useRef(null);
  const splitTextRef = useRef(null);
  const timelineRef = useRef(null);
  const [SplitText, setSplitText] = useState(null);

  // Utiliser la ref externe si fournie, sinon utiliser la ref interne
  const targetRef = ref || elementRef;

  // Charger SplitText dynamiquement
  useEffect(() => {
    if (typeof window !== 'undefined' && !SplitText) {
      import('gsap/SplitText').then((module) => {
        setSplitText(() => module.SplitText);
        gsap.registerPlugin(module.SplitText);
      });
    }
  }, [SplitText]);

  useEffect(() => {
    if (!targetRef.current || !SplitText) return;

    const element = targetRef.current;
    
    // Nettoyer les animations précédentes
    if (timelineRef.current) {
      timelineRef.current.kill();
    }
    if (splitTextRef.current) {
      splitTextRef.current.revert();
    }

    // Créer le SplitText
    splitTextRef.current = new SplitText(element, {
      type: splitBy,
      linesClass: 'gsap-line',
      wordsClass: 'gsap-word',
      charsClass: 'gsap-char'
    });

    const targets = splitTextRef.current[splitBy];

    if (!targets || targets.length === 0) return;

    // Pour l'effet reveal, on utilise clip-path au lieu d'overflow hidden
    // Cela évite de couper les jambages des lettres et préserve l'espacement
    if (splitBy === 'chars') {
      // Pour les caractères, on applique le clip-path directement
      targets.forEach((target) => {
        target.style.clipPath = 'inset(0 0 100% 0)';
        target.style.display = 'inline-block';
      });
    } else {
      // Pour les mots et lignes, on peut utiliser des wrappers
      targets.forEach((target, index) => {
        const wrapper = document.createElement('div');
        wrapper.className = 'gsap-reveal-wrapper';
        wrapper.style.overflow = 'hidden';
        wrapper.style.display = splitBy === 'lines' ? 'block' : 'inline-block';

        target.parentNode.insertBefore(wrapper, target);
        wrapper.appendChild(target);
      });
    }

    // Créer la timeline
    timelineRef.current = gsap.timeline({
      paused: !triggerOnScroll,
      onComplete,
      onStart
    });

    // Définir l'état initial
    gsap.set(targets, from);

    // Créer l'animation
    timelineRef.current.to(targets, {
      ...to,
      duration,
      ease,
      stagger,
      delay
    });

    // Configurer ScrollTrigger si nécessaire
    if (triggerOnScroll) {
      ScrollTrigger.create({
        trigger: element,
        start: triggerStart,
        once: triggerOnce,
        onEnter: () => timelineRef.current.play(),
        onLeave: () => {
          if (!triggerOnce) timelineRef.current.reverse();
        },
        onEnterBack: () => {
          if (!triggerOnce) timelineRef.current.play();
        }
      });
    }

    // Cleanup function
    return () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
      if (splitTextRef.current) {
        splitTextRef.current.revert();
      }
      ScrollTrigger.getAll().forEach(trigger => {
        if (trigger.trigger === element) {
          trigger.kill();
        }
      });
    };
  }, [children, splitBy, stagger, duration, delay, ease, triggerOnScroll, triggerStart, triggerOnce, SplitText]);

  // Fonction pour déclencher manuellement l'animation
  const play = () => {
    if (timelineRef.current) {
      timelineRef.current.play();
    }
  };

  const reverse = () => {
    if (timelineRef.current) {
      timelineRef.current.reverse();
    }
  };

  const restart = () => {
    if (timelineRef.current) {
      timelineRef.current.restart();
    }
  };

  // Exposer les méthodes via la ref
  useEffect(() => {
    if (ref && typeof ref === 'object') {
      ref.current = {
        ...targetRef.current,
        play,
        reverse,
        restart,
        timeline: timelineRef.current,
        splitText: splitTextRef.current
      };
    }
  });

  return (
    <Tag
      ref={targetRef}
      className={`${styles.gsapTextReveal} ${className}`}
      {...props}
    >
      {children}
    </Tag>
  );
});

GSAPTextReveal.displayName = 'GSAPTextReveal';

export default GSAPTextReveal;
