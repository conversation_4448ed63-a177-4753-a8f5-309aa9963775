/**
 * Presets d'animation pour GSAPTextReveal
 * Facilite l'utilisation avec des configurations prédéfinies
 */

export const ANIMATION_PRESETS = {
  // Animation par défaut - révélation douce par caractères
  default: {
    splitBy: 'chars',
    stagger: 0.02,
    duration: 0.6,
    ease: 'power2.out',
    from: { y: 100, opacity: 0 },
    to: { y: 0, opacity: 1 }
  },

  // Animation rapide et énergique
  energetic: {
    splitBy: 'chars',
    stagger: 0.01,
    duration: 0.4,
    ease: 'back.out(1.7)',
    from: { y: 50, opacity: 0, scale: 0.8 },
    to: { y: 0, opacity: 1, scale: 1 }
  },

  // Animation élégante avec rotation
  elegant: {
    splitBy: 'chars',
    stagger: 0.03,
    duration: 0.8,
    ease: 'power3.out',
    from: { y: 100, opacity: 0, rotationX: 90 },
    to: { y: 0, opacity: 1, rotationX: 0 }
  },

  // Animation par mots pour les titres courts
  words: {
    splitBy: 'words',
    stagger: 0.1,
    duration: 0.6,
    ease: 'power2.out',
    from: { y: 60, opacity: 0 },
    to: { y: 0, opacity: 1 }
  },

  // Animation par lignes pour les paragraphes
  lines: {
    splitBy: 'lines',
    stagger: 0.15,
    duration: 0.8,
    ease: 'power2.out',
    from: { y: 100, opacity: 0 },
    to: { y: 0, opacity: 1 }
  },

  // Animation de machine à écrire
  typewriter: {
    splitBy: 'chars',
    stagger: 0.05,
    duration: 0.1,
    ease: 'none',
    from: { opacity: 0 },
    to: { opacity: 1 }
  },

  // Animation de glitch/cyberpunk
  glitch: {
    splitBy: 'chars',
    stagger: 0.02,
    duration: 0.3,
    ease: 'power2.inOut',
    from: { 
      y: 30, 
      opacity: 0, 
      skewX: 10,
      filter: 'blur(2px)'
    },
    to: { 
      y: 0, 
      opacity: 1, 
      skewX: 0,
      filter: 'blur(0px)'
    }
  },

  // Animation douce pour les sous-titres
  subtitle: {
    splitBy: 'words',
    stagger: 0.08,
    duration: 0.7,
    ease: 'power2.out',
    delay: 0.3,
    from: { y: 40, opacity: 0 },
    to: { y: 0, opacity: 1 }
  },

  // Animation pour les titres principaux
  hero: {
    splitBy: 'chars',
    stagger: 0.015,
    duration: 0.8,
    ease: 'power3.out',
    from: { 
      y: 120, 
      opacity: 0, 
      rotationX: 90,
      transformOrigin: 'center bottom'
    },
    to: { 
      y: 0, 
      opacity: 1, 
      rotationX: 0,
      transformOrigin: 'center bottom'
    }
  }
};

/**
 * Fonction utilitaire pour obtenir un preset avec des overrides
 * @param {string} presetName - Nom du preset
 * @param {Object} overrides - Propriétés à surcharger
 * @returns {Object} Configuration finale
 */
export const getPreset = (presetName, overrides = {}) => {
  const preset = ANIMATION_PRESETS[presetName];
  if (!preset) {
    console.warn(`Preset "${presetName}" not found. Using default preset.`);
    return { ...ANIMATION_PRESETS.default, ...overrides };
  }
  
  return {
    ...preset,
    ...overrides,
    from: { ...preset.from, ...overrides.from },
    to: { ...preset.to, ...overrides.to }
  };
};
