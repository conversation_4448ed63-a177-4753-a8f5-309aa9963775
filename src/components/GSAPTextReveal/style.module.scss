// Styles pour le composant GSAPTextReveal
.gsapTextReveal {
  // Le conteneur hérite des styles du tag parent
  
  // Styles pour les éléments splitText générés par GSAP
  :global(.gsap-line) {
    overflow: hidden;
    display: block;
  }

  :global(.gsap-word) {
    display: inline-block;
    white-space: nowrap;
  }

  :global(.gsap-char) {
    display: inline-block;
    transform-origin: center bottom;
    
    // Force l'héritage complet des propriétés typographiques
    font-size: inherit !important;
    font-weight: inherit !important;
    font-family: inherit !important;
    letter-spacing: inherit !important;
    word-spacing: inherit !important;
    line-height: inherit !important;
    color: inherit !important;
    text-transform: inherit !important;
    font-style: inherit !important;
  }

  // Préservation de l'espacement pour les caractères spéciaux
  :global(.gsap-char[data-char=" "]) {
    width: 0.25em;
  }

  // Styles pour améliorer les performances d'animation
  :global(.gsap-line),
  :global(.gsap-word),
  :global(.gsap-char) {
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
  }
}

// Classe utilitaire pour désactiver temporairement les animations
.gsapTextReveal.no-animation {
  :global(.gsap-char) {
    transform: none !important;
    opacity: 1 !important;
  }
}
