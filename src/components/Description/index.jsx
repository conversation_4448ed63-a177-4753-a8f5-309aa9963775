    import styles from './style.module.scss';
    import { useInView, motion } from 'framer-motion';
    import { useRef } from 'react';
    import { opacity, slideUpWithOpacity } from './animation';
    import Rounded from '../../common/RoundedButton';
    import { useLenisParallax } from '@/hooks/useLenisParallax';
    import GSAPTextReveal from '@/components/GSAPTextReveal';
    import { getPreset } from '@/components/GSAPTextReveal/presets';

    export default function Description({
        descriptionTitle = "Helping brands to stand out in the digital era. Together we will set the new status quo. No nonsense, always on the cutting edge.",
        descriptionText = "The combination of my passion for design, code & interaction positions me in a unique place in the web design world.",
        showButton = true,
        buttonText = "About me",
        titleTag = "h2" // Nouveau prop pour spécifier le niveau de titre
    }) {

        const description = useRef(null);
        const isInView = useInView(description);
        const parallaxRef = useLenisParallax(0.1);

        // C<PERSON>er le composant de titre dynamique
        const TitleComponent = titleTag;

        return (
            <div ref={description} className={`${styles.description} container medium`}>
                <div className={styles.body}>
                                          <GSAPTextReveal
                  as="h2"
                  {...getPreset('hero')}
                >
                  {descriptionTitle}
                </GSAPTextReveal>

                    <motion.div
                        variants={opacity}
                        animate={isInView ? "open" : "closed"}
                        className={styles.descriptionText}
                    >
                        {typeof descriptionText === 'string' ? (
                            <div className={styles.mask}>
                                <motion.p
                                    variants={slideUpWithOpacity}
                                    animate={isInView ? "open" : "closed"}
                                    className="text-big"
                                >
                                    {descriptionText}
                                </motion.p>
                            </div>
                        ) : (
                            <div className={styles.mask}>
                                <motion.div
                                    variants={slideUpWithOpacity}
                                    animate={isInView ? "open" : "closed"}
                                    className="text-big"
                                >
                                    {descriptionText}
                                </motion.div>
                            </div>
                        )}
                    </motion.div>
                    {showButton && (
                        <div ref={parallaxRef}>
                            <Rounded className={styles.button}>
                                <p>{buttonText}</p>
                            </Rounded>
                        </div>
                    )}
                </div>
            </div>
        );
    }
