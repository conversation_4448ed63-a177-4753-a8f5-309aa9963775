    import styles from './style.module.scss';
    import { useRef } from 'react';
    import Rounded from '../../common/RoundedButton';
    import { useLenisParallax } from '@/hooks/useLenisParallax';
    import GSAPTextReveal from '@/components/GSAPTextReveal';
    import { getPreset } from '@/components/GSAPTextReveal/presets';
    import { getPreset } from '@/components/GSAPTextReveal/presets';

    export default function Description({
        descriptionTitle = "Helping brands to stand out in the digital era. Together we will set the new status quo. No nonsense, always on the cutting edge.",
        descriptionText = "The combination of my passion for design, code & interaction positions me in a unique place in the web design world.",
        showButton = true,
        buttonText = "About me",
        titleTag = "h2" // Nouveau prop pour spécifier le niveau de titre
    }) {

        const description = useRef(null);
        const isInView = useInView(description);
        const parallaxRef = useLenisParallax(0.1);

        // Créer le composant de titre dynamique
        const TitleComponent = titleTag;

        return (
            <div ref={description} className={`${styles.description} container medium`}>
                <div className={styles.body}>
                                          <GSAPTextReveal
                  as="h2"
                  {...getPreset('hero')}
                >
                  {descriptionTitle}
                </GSAPTextReveal>

                    <div className={styles.descriptionText}>
                        {typeof descriptionText === 'string' ? (
                            <GSAPTextReveal
                                as="p"
                                className="text-big"
                                {...getPreset('lines', { delay: 0.8, stagger: 0.2 })}
                            >
                                {descriptionText}
                            </GSAPTextReveal>
                        ) : (
                            <GSAPTextReveal
                                as="div"
                                className="text-big"
                                {...getPreset('lines', { delay: 0.8, stagger: 0.2 })}
                            >
                                {descriptionText}
                            </GSAPTextReveal>
                        )}
                    </div>
                    {showButton && (
                        <div ref={parallaxRef}>
                            <Rounded className={styles.button}>
                                <p>{buttonText}</p>
                            </Rounded>
                        </div>
                    )}
                </div>
            </div>
        );
    }
